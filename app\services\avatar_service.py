"""
头像存储服务

支持多种存储方案：
1. 本地存储 - 适合小规模部署
2. 阿里云OSS - 适合中大规模部署
3. 腾讯云COS - 备选方案
4. 七牛云 - 备选方案
"""

from fastapi import UploadFile
from typing import Optional, Dict, Any, Tuple
from tortoise.transactions import in_transaction
import os
import uuid
import hashlib
from datetime import datetime
from PIL import Image
import logging

from app.models.user import User, UserAvatar
from app.utils.config import settings

logger = logging.getLogger(__name__)


class AvatarService:
    """头像存储服务类"""
    
    # 支持的图片格式
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
    ALLOWED_MIME_TYPES = {
        'image/jpeg', 'image/png', 'image/gif', 'image/webp'
    }
    
    # 文件大小限制（5MB）
    MAX_FILE_SIZE = 5 * 1024 * 1024
    
    # 头像尺寸配置
    AVATAR_SIZES = {
        'original': None,  # 保持原始尺寸
        'large': (400, 400),
        'medium': (200, 200),
        'small': (100, 100),
        'thumbnail': (50, 50)
    }

    @classmethod
    async def upload_avatar(cls, user: User, file: UploadFile) -> Dict[str, Any]:
        """上传用户头像"""
        
        # 验证文件
        await cls._validate_file(file)
        
        # 读取文件内容
        file_content = await file.read()
        
        # 获取图片信息
        image_info = await cls._get_image_info(file_content, file.filename)
        
        # 生成文件路径
        file_path = cls._generate_file_path(user.id, file.filename)
        
        async with in_transaction():
            # 停用用户的所有旧头像
            await user.deactivate_all_avatars()
            
            # 保存文件
            storage_info = await cls._save_file(file_content, file_path)
            
            # 创建头像记录
            avatar = await UserAvatar.create(
                user=user,
                original_filename=file.filename,
                file_path=storage_info['file_path'],
                file_size=len(file_content),
                mime_type=file.content_type,
                width=image_info['width'],
                height=image_info['height'],
                is_active=True,
                storage_type=storage_info['storage_type'],
                cdn_url=storage_info.get('cdn_url')
            )
            
            # 更新用户头像字段
            user.avatar = storage_info.get('cdn_url') or storage_info['file_path']
            await user.save()
            
            logger.info(f"用户 {user.username} 上传了新头像: {file.filename}")
            
            return {
                "avatar_id": avatar.id,
                "file_path": avatar.file_path,
                "cdn_url": avatar.cdn_url,
                "file_size": avatar.file_size,
                "width": avatar.width,
                "height": avatar.height
            }

    @classmethod
    async def _validate_file(cls, file: UploadFile):
        """验证上传文件"""
        
        # 检查文件扩展名
        if file.filename:
            ext = os.path.splitext(file.filename)[1].lower()
            if ext not in cls.ALLOWED_EXTENSIONS:
                raise ValueError(f"不支持的文件格式，支持的格式：{', '.join(cls.ALLOWED_EXTENSIONS)}")
        
        # 检查MIME类型
        if file.content_type not in cls.ALLOWED_MIME_TYPES:
            raise ValueError(f"不支持的文件类型：{file.content_type}")
        
        # 检查文件大小
        file.file.seek(0, 2)  # 移动到文件末尾
        file_size = file.file.tell()
        file.file.seek(0)  # 重置到文件开头
        
        if file_size > cls.MAX_FILE_SIZE:
            raise ValueError(f"文件大小超过限制，最大允许 {cls.MAX_FILE_SIZE // (1024*1024)}MB")
        
        if file_size == 0:
            raise ValueError("文件为空")

    @classmethod
    async def _get_image_info(cls, file_content: bytes, filename: str) -> Dict[str, Any]:
        """获取图片信息"""
        try:
            from io import BytesIO
            image = Image.open(BytesIO(file_content))
            
            return {
                'width': image.width,
                'height': image.height,
                'format': image.format,
                'mode': image.mode
            }
        except Exception as e:
            logger.error(f"获取图片信息失败: {e}")
            raise ValueError("无效的图片文件")

    @classmethod
    def _generate_file_path(cls, user_id: int, filename: str) -> str:
        """生成文件存储路径"""
        
        # 获取文件扩展名
        ext = os.path.splitext(filename)[1].lower()
        
        # 生成唯一文件名
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d")
        
        # 构建路径：avatars/年月日/用户ID/唯一ID.扩展名
        file_path = f"avatars/{timestamp}/{user_id}/{unique_id}{ext}"
        
        return file_path

    @classmethod
    async def _save_file(cls, file_content: bytes, file_path: str) -> Dict[str, Any]:
        """保存文件到存储系统"""
        
        # 根据配置选择存储方式
        storage_type = settings.get("avatar.storage_type", "local")
        
        if storage_type == "local":
            return await cls._save_to_local(file_content, file_path)
        elif storage_type == "oss":
            return await cls._save_to_oss(file_content, file_path)
        elif storage_type == "cos":
            return await cls._save_to_cos(file_content, file_path)
        elif storage_type == "qiniu":
            return await cls._save_to_qiniu(file_content, file_path)
        else:
            raise ValueError(f"不支持的存储类型: {storage_type}")

    @classmethod
    async def _save_to_local(cls, file_content: bytes, file_path: str) -> Dict[str, Any]:
        """保存到本地存储"""
        
        # 获取本地存储根目录
        upload_dir = settings.get("avatar.local_path", "uploads")
        full_path = os.path.join(upload_dir, file_path)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        # 写入文件
        with open(full_path, 'wb') as f:
            f.write(file_content)
        
        # 生成访问URL
        base_url = settings.get("avatar.base_url", "/uploads")
        access_url = f"{base_url}/{file_path}"
        
        return {
            "storage_type": "local",
            "file_path": full_path,
            "cdn_url": access_url
        }

    @classmethod
    async def _save_to_oss(cls, file_content: bytes, file_path: str) -> Dict[str, Any]:
        """保存到阿里云OSS"""
        try:
            import oss2
            
            # 获取OSS配置
            access_key_id = settings.get("oss.access_key_id")
            access_key_secret = settings.get("oss.access_key_secret")
            endpoint = settings.get("oss.endpoint")
            bucket_name = settings.get("oss.bucket_name")
            
            if not all([access_key_id, access_key_secret, endpoint, bucket_name]):
                raise ValueError("OSS配置不完整")
            
            # 创建OSS客户端
            auth = oss2.Auth(access_key_id, access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)
            
            # 上传文件
            result = bucket.put_object(file_path, file_content)
            
            # 生成CDN URL
            cdn_domain = settings.get("oss.cdn_domain")
            if cdn_domain:
                cdn_url = f"https://{cdn_domain}/{file_path}"
            else:
                cdn_url = f"https://{bucket_name}.{endpoint.replace('https://', '')}/{file_path}"
            
            return {
                "storage_type": "oss",
                "file_path": file_path,
                "cdn_url": cdn_url
            }
            
        except ImportError:
            raise ValueError("OSS SDK未安装，请安装 oss2 包")
        except Exception as e:
            logger.error(f"上传到OSS失败: {e}")
            raise ValueError(f"上传失败: {str(e)}")

    @classmethod
    async def _save_to_cos(cls, file_content: bytes, file_path: str) -> Dict[str, Any]:
        """保存到腾讯云COS"""
        # 腾讯云COS实现
        # 这里只是示例，实际需要安装和配置COS SDK
        raise NotImplementedError("腾讯云COS存储暂未实现")

    @classmethod
    async def _save_to_qiniu(cls, file_content: bytes, file_path: str) -> Dict[str, Any]:
        """保存到七牛云"""
        # 七牛云实现
        # 这里只是示例，实际需要安装和配置七牛云SDK
        raise NotImplementedError("七牛云存储暂未实现")

    @classmethod
    async def get_avatar_history(
        cls,
        user: User,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """获取用户头像历史"""
        
        offset = (page - 1) * size
        
        # 获取总数
        total = await UserAvatar.filter(user=user).count()
        
        # 获取头像记录
        avatars = await UserAvatar.filter(user=user).order_by("-created_at").offset(offset).limit(size)
        
        # 计算总页数
        pages = (total + size - 1) // size
        
        return {
            "total": total,
            "page": page,
            "size": size,
            "pages": pages,
            "avatars": [
                {
                    "id": avatar.id,
                    "original_filename": avatar.original_filename,
                    "file_path": avatar.file_path,
                    "cdn_url": avatar.cdn_url,
                    "file_size": avatar.file_size,
                    "width": avatar.width,
                    "height": avatar.height,
                    "is_active": avatar.is_active,
                    "created_at": avatar.created_at
                }
                for avatar in avatars
            ]
        }

    @classmethod
    async def delete_avatar(cls, user: User, avatar_id: int):
        """删除指定头像"""
        
        avatar = await UserAvatar.filter(id=avatar_id, user=user).first()
        if not avatar:
            raise ValueError("头像不存在")
        
        if avatar.is_active:
            raise ValueError("不能删除当前使用的头像")
        
        async with in_transaction():
            # 删除文件（根据存储类型）
            await cls._delete_file(avatar.file_path, avatar.storage_type)
            
            # 删除数据库记录
            await avatar.delete()
            
            logger.info(f"用户 {user.username} 删除了头像: {avatar.original_filename}")

    @classmethod
    async def _delete_file(cls, file_path: str, storage_type: str):
        """删除存储文件"""
        
        if storage_type == "local":
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                logger.error(f"删除本地文件失败: {e}")
        
        # 其他存储类型的删除实现
        # TODO: 实现OSS、COS、七牛云的文件删除
